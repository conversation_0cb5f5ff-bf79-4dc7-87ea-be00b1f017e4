# Renovation App

一个使用 Next.js 15（App Router）与 React 19 构建的装修/建材商品浏览与商家管理应用。

## 功能概述
- 用户端：
  - 浏览商品列表、按分类筛选、查看商品详情。
- 商家端：
  - 商家仪表盘与资料管理。
  - 商品管理与新增商品。

代码入口：
- 用户首页：`/`（app/page.tsx -> components/user/user-homepage.tsx）
- 商家页面：`/merchant`（app/merchant/page.tsx -> components/merchant/merchant-dashboard.tsx）

## 技术栈
- Next.js 15（App Router）
- React 19
- TypeScript
- Tailwind CSS v4（含 shadcn/ui 与 Radix UI 组件）
- Recharts（图表）
- 包管理器：pnpm（通过 Corepack）

## 本地开发
### 环境要求
- Node.js 20（建议 20.x 及以上）
- pnpm（建议通过 Corepack 启用）

### 启动步骤
1. 安装依赖：
   - 若首次使用 pnpm，先启用 Corepack（仅需一次）：
     ```bash
     corepack enable  #进入超级管理员的powershell
     ```
   - 安装依赖：
     ```bash
     pnpm install
     ```
2. 启动开发服务：
   ```bash
   pnpm dev
   ```
3. 打开浏览器访问：http://localhost:3000

## 生产构建与启动
1. 构建：
   ```bash
   pnpm build
   ```
2. 启动：
   ```bash
   pnpm start -p 3000
   ```
   如需修改端口，可传入 `-p <port>` 或设置环境变量 `PORT`。

## 使用 Docker 容器化
本仓库已提供多阶段构建的 `Dockerfile`，用于最小化运行镜像体积并隔离构建依赖。

### 构建镜像
```bash
# 在项目根目录执行
docker build -t renovation-app:latest .
```

### 运行容器
```bash
docker run --rm -it -p 3000:3000 --name renovation-app renovation-app:latest
```
- 默认暴露并监听 3000 端口。
- 如需修改容器内端口：
  ```bash
  docker run -e PORT=4000 -p 4000:4000 renovation-app:latest
  ```

## 目录结构概览
- app/：Next.js App Router 页面与布局
  - app/page.tsx：用户首页入口
  - app/merchant/page.tsx：商家页面入口
- components/：通用与业务组件
  - components/user/*：用户端组件（商品列表、详情等）
  - components/merchant/*：商家端组件（仪表盘、商品管理等）
  - components/ui/*：UI 基础组件库封装
- public/：静态资源
- scripts/：SQL 样例脚本

## 备注
- 本项目的 Next.js 构建配置（next.config.mjs）关闭了构建期 ESLint/TS 错误拦截并设置了 `images.unoptimized = true`，便于本地与容器环境快速构建与运行。
- 若你在中国大陆网络环境下构建 Docker 镜像速度较慢，可考虑配置镜像加速源。